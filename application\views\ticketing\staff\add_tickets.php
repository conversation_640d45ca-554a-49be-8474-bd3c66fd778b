<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('ParentTicket_dashboard');?>">Parent Ticketing</a></li>
    <li>File New Ticket</li>
</ul>





<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-10">
                   <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('ParentTicket_dashboard');?>"><span class="fa fa-arrow-left"></span></a>File New Ticket (On Behalf of Parent)</h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form enctype="multipart/form-data" autocomplete="off" method="post" id="issue-form" action="<?php echo site_url('parent_ticketing/submit_ticket_staff'); ?>" data-parsley-validate="" class="form-horizontal" >
                <!-- <h4>On behalf of Parent</h4> -->
                <div class="col-md-8 col-md-offset-2">                        
                        <div class="form-group">
                            <label class=" col-md-12">Class / Section<font color="red">*</font></label>
                            <div class="col-md-12">
                                <?php 
                                $array = array();
                                $array[0] = 'Select Section';
                                foreach ($classSectionList as $key => $cl) {
                                    $array[$cl->class_id.'_'.$cl->id] = $cl->class_name . '('.$cl->section_name.')';
                                }
                                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' required class='form-control'");
                                  ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 " for="hospitalization_date">Student Name<font color="red">*</font></label>
                            <div class="col-md-12">
                                     <select required="" class="form-control" placeholder="Select Student Name" data-parsley-required-message="Please enter Student Name" id="user_selection" name="user_selection">
                                        <option value="">Select Student</option>
                                     </select>
                                <span class="help-block">Select class and get student</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea3" required="">Parent<font color="red">*</font></label>
                            <div class="col-md-12">
                                <select required="" class="form-control" id="parent_selection" data-parsley-required-message="Please enter Parent Name" name="parent_id">
                                    <option value="">Select Parent</option>
                                </select>
                                <span class="help-block">Select student and get parent</span> 
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 " for="discharge_date">Type<font color="red">*</font></label>
                            <div class="col-md-12">
                                <div class="input-group">
                                   <select name="issue_type" id="issue_type" class="form-control">
                                    <?php
                                        foreach ($ticket_types as $type) {
                                            echo "<option value='$type'>$type</option>";
                                        }
                                    ?>
                                 </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea2" required="">Category<font color="red">*</font></label>
                            <div class="col-md-12">
                               <select name="category" id="category" class="form-control" required>
                                <option value="">Select Category</option>
                                    <?php
                                        foreach ($category_list as $cat) {
                                            $selected = $cat->is_default?'selected':'';
                                            echo "<option value='$cat->id' $selected>$cat->name</option>";
                                        }
                                    ?>
                                </select>
                            <?php
                                if (empty($category_list)) {
                                    echo "<p class='help-block small'>Categories are not configured by the institute.</p>";
                                }
                            ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea1" required="">Title</label>
                            <div class="col-md-12">
                            <input type="text" name="title" id="title" placeholder="Enter title" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea1" required="">Description</label>
                            <div class="col-md-12">
                           <textarea placeholder="Enter description" rows="10" class="form-control" name="description" id="description"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea1" required="">Attachments</label>
                            <div class="col-md-12">
                            <input type="file" name="attachments[]" id="attachments" multiple="">
                            <span class="help-block">You can upload multiple files</span>
                            </div>
                        </div>
                        <center>
                            <input type="button" id="subBtn" style="width:138px;" class="btn btn-primary" onclick="please_wait()" value="Submit">
                            <a style="width:138px;" href="<?php echo site_url('ParentTicket_dashboard'); ?>" class='btn btn-warning mrg'>Cancel</a>
                        </center>
                </div>
            </form>

        </div>
    </div>
</div>
























<script>

function please_wait(){
    var $form = $('#issue-form');
    if ($form.parsley().validate()){       
        $("#subBtn").prop('disabled', true).val('Please wait...');
        $('#issue-form').submit(); 
    }
}



$("#classSectionId").on('change', function(){    
        var cls_section = $('#classSectionId').val();
        $.ajax({
            url: '<?php echo site_url('parent_ticketing/get_class_section_wise_std_data'); ?>',
            type: "post",
            data :{'cls_section':cls_section},
            success: function (data) {
                var list = $.parseJSON(data);
               
                option = '';
                option += '<option value="">Select Student</option>';
                for (i = 0; i < list.length; i++) {
                    option +='<option value="'+list[i].sId+'">'+list[i].sName+'</option>'
                    console.log()
                }
                $('#user_selection').html(option);
                

              },
        });
    });

$("#user_selection").on('change', function(){    
   var student_id = $('#user_selection').val();
    $.ajax({
        url: '<?php echo site_url('parent_ticketing/get_parent_detailsbystudentid'); ?>',
        type: "post",
        data :{'student_id':student_id},
        success: function (data) {
            var parentlist = $.parseJSON(data);
            option = '<option value="">Select Parent</option>';
            for (i = 0; i < parentlist.length; i++) {
                option +='<option value="'+parentlist[i].parent_id+'">'+parentlist[i].parentName+' ('+parentlist[i].relation_type+')'+'</option>'
            }
            $('#parent_selection').html(option);
            

          },
    });
 });

</script>

<style type="text/css">
.form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0.5rem;
    text-align: right;
    font-weight: 600;
}
.help-block{
    font-weight: 200;
}
</style>



















