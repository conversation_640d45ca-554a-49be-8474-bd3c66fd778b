<ul class="breadcrumb">
    <li><a href="<?php echo site_url('dashboard');?>">Dashboard</a></li>
    <li><a href="<?php echo site_url('ParentTicket_dashboard');?>">Parent Ticketing</a></li>
    <li>File New Ticket</li>
</ul>





<div class="col-md-12">
    <div class="card cd_border">
        <div class="card-header panel_heading_new_style_staff_border">
            <div class="row" style="margin: 0px">
                <div class="col-md-10">
                   <h3 class="card-title panel_title_new_style_staff"><a class="back_anchor" href="<?php echo site_url('ParentTicket_dashboard');?>"><span class="fa fa-arrow-left"></span></a>File New Ticket (On Behalf of Parent)</h3>
                </div>
            </div>
        </div>
        <div class="card-body">
            <form autocomplete="off" method="post" id="issue-form" data-parsley-validate="" class="form-horizontal" >
                <!-- <h4>On behalf of Parent</h4> -->
                <div class="col-md-8 col-md-offset-2">                        
                        <div class="form-group">
                            <label class=" col-md-12">Class / Section<font color="red">*</font></label>
                            <div class="col-md-12">
                                <?php 
                                $array = array();
                                $array[0] = 'Select Section';
                                foreach ($classSectionList as $key => $cl) {
                                    $array[$cl->class_id.'_'.$cl->id] = $cl->class_name . '('.$cl->section_name.')';
                                }
                                echo form_dropdown("classSectionId", $array, '', "id='classSectionId' required class='form-control'");
                                  ?>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 " for="hospitalization_date">Student Name<font color="red">*</font></label>
                            <div class="col-md-12">
                                     <select required="" class="form-control" placeholder="Select Student Name" data-parsley-required-message="Please enter Student Name" id="user_selection" name="user_selection">
                                        <option value="">Select Student</option>
                                     </select>
                                <span class="help-block">Select class and get student</span>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea3" required="">Parent<font color="red">*</font></label>
                            <div class="col-md-12">
                                <select required="" class="form-control" id="parent_selection" data-parsley-required-message="Please enter Parent Name" name="parent_id">
                                    <option value="">Select Parent</option>
                                </select>
                                <span class="help-block">Select student and get parent</span> 
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12 " for="discharge_date">Type<font color="red">*</font></label>
                            <div class="col-md-12">
                                <div class="input-group">
                                   <select name="issue_type" id="issue_type" class="form-control">
                                    <?php
                                        foreach ($ticket_types as $type) {
                                            echo "<option value='$type'>$type</option>";
                                        }
                                    ?>
                                 </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea2" required="">Category<font color="red">*</font></label>
                            <div class="col-md-12">
                               <select name="category" id="category" class="form-control" required>
                                <option value="">Select Category</option>
                                    <?php
                                        foreach ($category_list as $cat) {
                                            $selected = $cat->is_default?'selected':'';
                                            echo "<option value='$cat->id' $selected>$cat->name</option>";
                                        }
                                    ?>
                                </select>
                            <?php
                                if (empty($category_list)) {
                                    echo "<p class='help-block small'>Categories are not configured by the institute.</p>";
                                }
                            ?>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea1" required="">Title</label>
                            <div class="col-md-12">
                            <input type="text" name="title" id="title" placeholder="Enter title" class="form-control">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea1" required="">Description</label>
                            <div class="col-md-12">
                           <textarea placeholder="Enter description" rows="10" class="form-control" name="description" id="description"></textarea>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-md-12" for="exampleFormControlTextarea1" required="">Attachments</label>
                            <div class="col-md-12">
                            <input type="file" id="attachments" multiple="">
                            <span class="help-block">You can upload multiple files</span>
                            <div id="file-list" style="display: none; margin-top: 10px;">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th>File Name</th>
                                            <th>Status</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="file-table">
                                    </tbody>
                                </table>
                            </div>
                            <div id="upload-progress" style="display: none; margin-top: 10px;">
                                <div class="progress">
                                    <div class="progress-bar" role="progressbar" style="width: 0%">0%</div>
                                </div>
                            </div>
                            </div>
                        </div>
                        <center>
                            <input type="button" id="subBtn" style="width:138px;" class="btn btn-primary" onclick="submitTicket()" value="Submit">
                            <a style="width:138px;" href="<?php echo site_url('ParentTicket_dashboard'); ?>" class='btn btn-warning mrg'>Cancel</a>
                        </center>
                </div>
            </form>

        </div>
    </div>
</div>
























<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>

var uploadedFilePaths = [];

function submitTicket(){
    var $form = $('#issue-form');
    if ($form.parsley().validate()){
        $("#subBtn").prop('disabled', true).val('Please wait...');
        submitFormWithPaths(uploadedFilePaths);
    }
}

function uploadFileToS3(file, fileIndex) {
    return new Promise((resolve, reject) => {
        // Show SweetAlert loading modal
        Swal.fire({
            title: 'Uploading File...',
            text: 'Uploading "' + file.name + '"',
            allowOutsideClick: false,
            allowEscapeKey: false,
            showConfirmButton: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Show uploading status for this specific file
        updateFileStatus(fileIndex, 'Uploading...', 'text-warning');

        // Step 1: Get signed URL
        $.ajax({
            url: '<?php echo site_url("S3_controller/getSignedUrl"); ?>',
            type: 'POST',
            data: {
                filename: file.name,
                file_type: file.type,
                folder: 'tickets'
            },
            success: function (response) {
                let data = JSON.parse(response);
                if (!data.signedUrl || !data.path) {
                    Swal.close();
                    updateFileStatus(fileIndex, 'Failed', 'text-danger');
                    Swal.fire('Upload Failed', 'Server did not return a valid upload URL.', 'error');
                    reject('Invalid response');
                    return;
                }

                // Step 2: Upload file to S3
                $.ajax({
                    url: data.signedUrl,
                    type: 'PUT',
                    headers: {
                        "Content-Type": file.type,
                        'x-amz-acl': 'public-read'
                    },
                    processData: false,
                    data: file,
                    success: function (_, status, xhr) {
                        Swal.close();
                        if (xhr.status == 200 || xhr.status == 201) {
                            updateFileStatus(fileIndex, 'Uploaded', 'text-success');
                            Swal.fire({
                                icon: 'success',
                                title: 'Upload Successful',
                                text: '"' + file.name + '" uploaded successfully',
                                timer: 1500,
                                showConfirmButton: false
                            });
                            resolve({ path: data.path, name: file.name });
                        } else {
                            updateFileStatus(fileIndex, 'Failed', 'text-danger');
                            Swal.fire('Upload Failed', 'Upload finished with unexpected status: ' + xhr.status, 'error');
                            reject('Unexpected status');
                        }
                    },
                    error: function (xhr, status, err) {
                        Swal.close();
                        console.error('Upload Error:', err);
                        updateFileStatus(fileIndex, 'Failed', 'text-danger');
                        Swal.fire('Upload Failed', 'There was an error uploading "' + file.name + '"', 'error');
                        reject(err);
                    }
                });
            },
            error: function () {
                Swal.close();
                updateFileStatus(fileIndex, 'Failed', 'text-danger');
                Swal.fire('Error', 'Failed to get signed URL from server.', 'error');
                reject('Request failed');
            }
        });
    });
}

function updateFileStatus(fileIndex, status, className) {
    $('#file_' + fileIndex + ' .file-status').html('<span class="' + className + '">' + status + '</span>');
}

function submitFormWithPaths(filePaths) {
    var formData = {
        classSectionId: $('#classSectionId').val(),
        user_selection: $('#user_selection').val(),
        parent_id: $('#parent_selection').val(),
        issue_type: $('#issue_type').val(),
        category: $('#category').val(),
        title: $('#title').val(),
        description: $('#description').val(),
        file_paths: filePaths
    };

    $.ajax({
        url: '<?php echo site_url('parent_ticketing/submit_ticket_staff_with_paths'); ?>',
        type: 'POST',
        data: formData,
        success: function(response) {
            Swal.fire({
                icon: 'success',
                title: 'Ticket Created Successfully!',
                showConfirmButton: false,
                timer: 2000
            }).then(() => {
                window.location.href = '<?php echo site_url('parent_ticketing/view_assigned_tickets'); ?>';
            });
        },
        error: function(xhr, status, err) {
            console.error('Submit Error:', err);
            $("#subBtn").prop('disabled', false).val('Submit');
            Swal.fire('Submission Failed', 'There was an error submitting the ticket.', 'error');
        }
    });
}

$("#classSectionId").on('change', function(){
        var cls_section = $('#classSectionId').val();
        $.ajax({
            url: '<?php echo site_url('parent_ticketing/get_class_section_wise_std_data'); ?>',
            type: "post",
            data :{'cls_section':cls_section},
            success: function (data) {
                var list = $.parseJSON(data);

                option = '';
                option += '<option value="">Select Student</option>';
                for (i = 0; i < list.length; i++) {
                    option +='<option value="'+list[i].sId+'">'+list[i].sName+'</option>'
                    console.log()
                }
                $('#user_selection').html(option);


              },
        });
    });

$("#user_selection").on('change', function(){
   var student_id = $('#user_selection').val();
    $.ajax({
        url: '<?php echo site_url('parent_ticketing/get_parent_detailsbystudentid'); ?>',
        type: "post",
        data :{'student_id':student_id},
        success: function (data) {
            var parentlist = $.parseJSON(data);
            option = '<option value="">Select Parent</option>';
            for (i = 0; i < parentlist.length; i++) {
                option +='<option value="'+parentlist[i].parent_id+'">'+parentlist[i].parentName+' ('+parentlist[i].relation_type+')'+'</option>'
            }
            $('#parent_selection').html(option);


          },
    });
 });

// File selection and immediate upload
var fileCounter = 0;

$('#attachments').change(function(){
    var files = this.files;

    for(var i = 0; i < files.length; i++) {
        var file = files[i];
        fileCounter++;

        if(alreadySelected(file.name)) {
            Swal.fire('Warning', 'File "' + file.name + '" already added', 'warning');
            continue;
        }

        // Add file to table immediately
        addFileToTable(file, fileCounter);

        // Upload file immediately
        uploadFileToS3(file, fileCounter).then((fileData) => {
            // Add to uploaded files array
            uploadedFilePaths.push(fileData);
        }).catch((err) => {
            console.error('Upload failed for file:', file.name, err);
        });
    }

    // Clear the input
    this.value = '';
});

function addFileToTable(file, index) {
    var html = '<tr id="file_' + index + '">';
    html += '<td style="word-break: break-all">' + file.name + '</td>';
    html += '<td class="file-status" style="width: 15%;">Preparing...</td>';
    html += '<td style="width: 10%;"><button style="width: 80px;" class="btn btn-sm btn-danger" onclick="removeFile(' + index + ',\'' + file.name + '\')">Remove</button></td>';
    html += '</tr>';

    $("#file-table").append(html);
    $('#file-list').css('display', 'table');
}

function alreadySelected(fileName) {
    for(var i = 0; i < uploadedFilePaths.length; i++) {
        if(uploadedFilePaths[i].name === fileName) {
            return true;
        }
    }
    return false;
}

function removeFile(index, fileName) {
    // Remove from uploadedFilePaths array
    for(var i = 0; i < uploadedFilePaths.length; i++) {
        if(uploadedFilePaths[i].name === fileName) {
            uploadedFilePaths.splice(i, 1);
            break;
        }
    }

    // Remove from table
    $('#file_' + index).remove();

    // Hide table if no files
    if($('#file-table tr').length === 0) {
        $('#file-list').css('display', 'none');
    }
}

</script>

<style type="text/css">
.form-horizontal .control-label {
    padding-top: 7px;
    margin-bottom: 0.5rem;
    text-align: right;
    font-weight: 600;
}
.help-block{
    font-weight: 200;
}
</style>



















